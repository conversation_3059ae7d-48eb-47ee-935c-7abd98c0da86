import { renderHook, act } from '@testing-library/react';
import { useChromeExtensionIntegration } from '../use-chrome-extension-integration';

// Mock Chrome APIs
const mockChrome = {
  runtime: {
    connect: jest.fn(),
    onMessage: {
      addListener: jest.fn(),
    },
  },
};

// Mock URL search params
const mockURLSearchParams = jest.fn();
Object.defineProperty(window, 'URLSearchParams', {
  writable: true,
  value: mockURLSearchParams,
});

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  writable: true,
  value: mockLocalStorage,
});

// Mock chrome global
(global as any).chrome = mockChrome;

describe('useChromeExtensionIntegration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockURLSearchParams.mockImplementation((search) => ({
      get: jest.fn((key) => {
        if (key === 'recording' && search === '?recording=test-session-123') {
          return 'test-session-123';
        }
        return null;
      }),
    }));
  });

  it('initializes with default state', () => {
    const { result } = renderHook(() => useChromeExtensionIntegration());

    expect(result.current.isConnected).toBe(false);
    expect(result.current.recordingData).toBe(null);
    expect(result.current.error).toBe(null);
  });

  it('detects recording session from URL parameters', () => {
    // Mock window.location.search
    Object.defineProperty(window, 'location', {
      writable: true,
      value: {
        search: '?recording=test-session-123',
      },
    });

    const mockPort = {
      postMessage: jest.fn(),
      onMessage: {
        addListener: jest.fn(),
      },
      onDisconnect: {
        addListener: jest.fn(),
      },
    };

    mockChrome.runtime.connect.mockReturnValue(mockPort);

    renderHook(() => useChromeExtensionIntegration());

    expect(mockChrome.runtime.connect).toHaveBeenCalled();
    expect(mockPort.postMessage).toHaveBeenCalledWith({
      action: 'getRecordingData',
      sessionId: 'test-session-123',
    });
  });

  it('handles successful recording data retrieval', async () => {
    const mockRecordingData = {
      sessionId: 'test-session-123',
      mouseData: [
        { action: 'move', coords: { x: 100, y: 100 }, timestamp: 0 },
        { action: 'click', coords: { x: 150, y: 120 }, timestamp: 100 },
      ],
      metadata: {
        startTime: 1000,
        endTime: 2000,
        duration: 1000,
        recordingDate: '2024-01-01T00:00:00.000Z',
      },
      videoFileName: 'test-recording.webm',
    };

    const mockPort = {
      postMessage: jest.fn(),
      onMessage: {
        addListener: jest.fn((callback) => {
          // Simulate successful response
          setTimeout(() => {
            callback({
              success: true,
              data: mockRecordingData,
            });
          }, 0);
        }),
      },
      onDisconnect: {
        addListener: jest.fn(),
      },
    };

    mockChrome.runtime.connect.mockReturnValue(mockPort);

    const { result } = renderHook(() => useChromeExtensionIntegration());

    await act(async () => {
      await result.current.loadRecordingFromExtension('test-session-123');
    });

    expect(result.current.isConnected).toBe(true);
    expect(result.current.recordingData).toEqual(mockRecordingData);
    expect(result.current.error).toBe(null);
  });

  it('handles recording data retrieval errors', async () => {
    const mockPort = {
      postMessage: jest.fn(),
      onMessage: {
        addListener: jest.fn((callback) => {
          setTimeout(() => {
            callback({
              success: false,
              error: 'Recording data not found',
            });
          }, 0);
        }),
      },
      onDisconnect: {
        addListener: jest.fn(),
      },
    };

    mockChrome.runtime.connect.mockReturnValue(mockPort);

    const { result } = renderHook(() => useChromeExtensionIntegration());

    await act(async () => {
      await result.current.loadRecordingFromExtension('invalid-session');
    });

    expect(result.current.error).toBe('Recording data not found');
    expect(result.current.recordingData).toBe(null);
  });

  it('retrieves cursor data for video files', () => {
    const mockCursorData = {
      id: 'test-cursor-track',
      mouseData: [{ action: 'move', coords: { x: 100, y: 100 }, timestamp: 0 }],
      metadata: {
        startTime: 1000,
        endTime: 2000,
        duration: 1000,
        recordingDate: '2024-01-01T00:00:00.000Z',
      },
    };

    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockCursorData));

    const { result } = renderHook(() => useChromeExtensionIntegration());

    const retrievedData = result.current.getCursorDataForVideo('test-video.webm');

    expect(mockLocalStorage.getItem).toHaveBeenCalledWith('cursor_data_file_test-video.webm');
    expect(retrievedData).toEqual(mockCursorData);
  });

  it('returns null for non-existent cursor data', () => {
    mockLocalStorage.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useChromeExtensionIntegration());

    const retrievedData = result.current.getCursorDataForVideo('non-existent.webm');

    expect(retrievedData).toBe(null);
  });

  it('clears cursor data correctly', () => {
    const mockRecordingData = {
      sessionId: 'test-session-123',
      videoFileName: 'test-video.webm',
      mouseData: [],
      metadata: {
        startTime: 1000,
        endTime: 2000,
        duration: 1000,
        recordingDate: '2024-01-01T00:00:00.000Z',
      },
    };

    const { result } = renderHook(() => useChromeExtensionIntegration());
    
    // Set recording data
    act(() => {
      (result.current as any).recordingData = mockRecordingData;
    });

    act(() => {
      result.current.clearCursorData('test-session-123');
    });

    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('cursor_data_test-session-123');
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('cursor_data_file_test-video.webm');
  });

  it('handles Chrome extension not available', async () => {
    // Remove chrome global
    delete (global as any).chrome;

    const { result } = renderHook(() => useChromeExtensionIntegration());

    await act(async () => {
      await result.current.loadRecordingFromExtension('test-session');
    });

    expect(result.current.error).toContain('Chrome extension not found');
  });

  it('handles malformed cursor data gracefully', () => {
    mockLocalStorage.getItem.mockReturnValue('invalid-json');

    const { result } = renderHook(() => useChromeExtensionIntegration());

    const retrievedData = result.current.getCursorDataForVideo('test-video.webm');

    expect(retrievedData).toBe(null);
  });
});
