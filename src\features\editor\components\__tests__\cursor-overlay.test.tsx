import React from 'react';
import { render, screen } from '@testing-library/react';
import { CursorOverlay } from '../cursor-overlay';
import { CursorTrackData } from '../../hooks/use-chrome-extension-integration';

// Mock Remotion's useCurrentFrame hook
jest.mock('remotion', () => ({
  useCurrentFrame: jest.fn(),
}));

const mockUseCurrentFrame = require('remotion').useCurrentFrame as jest.MockedFunction<typeof import('remotion').useCurrentFrame>;

const mockCursorData: CursorTrackData = {
  id: 'test-cursor-track',
  mouseData: [
    { action: 'move', coords: { x: 100, y: 100 }, timestamp: 0 },
    { action: 'move', coords: { x: 150, y: 120 }, timestamp: 100 },
    { action: 'click', coords: { x: 150, y: 120 }, timestamp: 200 },
    { action: 'move', coords: { x: 200, y: 150 }, timestamp: 300 },
    { action: 'move', coords: { x: 250, y: 180 }, timestamp: 400 },
  ],
  metadata: {
    startTime: 0,
    endTime: 500,
    duration: 500,
    recordingDate: '2024-01-01T00:00:00.000Z',
  },
};

describe('CursorOverlay', () => {
  beforeEach(() => {
    mockUseCurrentFrame.mockReturnValue(0);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(
      <CursorOverlay
        cursorData={mockCursorData}
        videoWidth={1920}
        videoHeight={1080}
        fps={30}
      />
    );
  });

  it('shows cursor at correct position for current frame', () => {
    // Frame 6 = 200ms at 30fps
    mockUseCurrentFrame.mockReturnValue(6);
    
    const { container } = render(
      <CursorOverlay
        cursorData={mockCursorData}
        videoWidth={1920}
        videoHeight={1080}
        fps={30}
      />
    );

    // Should show cursor at click position (150, 120)
    const cursorElement = container.querySelector('[style*="position: absolute"]');
    expect(cursorElement).toBeInTheDocument();
  });

  it('renders mouse path when showPath is true', () => {
    mockUseCurrentFrame.mockReturnValue(12); // 400ms
    
    const { container } = render(
      <CursorOverlay
        cursorData={mockCursorData}
        videoWidth={1920}
        videoHeight={1080}
        fps={30}
        showPath={true}
      />
    );

    const pathElement = container.querySelector('path');
    expect(pathElement).toBeInTheDocument();
  });

  it('does not render mouse path when showPath is false', () => {
    mockUseCurrentFrame.mockReturnValue(12);
    
    const { container } = render(
      <CursorOverlay
        cursorData={mockCursorData}
        videoWidth={1920}
        videoHeight={1080}
        fps={30}
        showPath={false}
      />
    );

    const pathElement = container.querySelector('path');
    expect(pathElement).not.toBeInTheDocument();
  });

  it('shows click indicators for recent clicks', () => {
    // Frame 7 = 233ms, should show click indicator from 200ms
    mockUseCurrentFrame.mockReturnValue(7);
    
    const { container } = render(
      <CursorOverlay
        cursorData={mockCursorData}
        videoWidth={1920}
        videoHeight={1080}
        fps={30}
      />
    );

    // Should have click indicator
    const clickIndicators = container.querySelectorAll('[style*="border-radius: 50%"]');
    expect(clickIndicators.length).toBeGreaterThan(0);
  });

  it('applies custom colors correctly', () => {
    mockUseCurrentFrame.mockReturnValue(12);
    
    const { container } = render(
      <CursorOverlay
        cursorData={mockCursorData}
        videoWidth={1920}
        videoHeight={1080}
        fps={30}
        pathColor="#ff0000"
        clickColor="#00ff00"
      />
    );

    const pathElement = container.querySelector('path');
    expect(pathElement).toHaveAttribute('stroke', '#ff0000');
  });

  it('scales cursor size correctly', () => {
    mockUseCurrentFrame.mockReturnValue(6);
    
    const { container } = render(
      <CursorOverlay
        cursorData={mockCursorData}
        videoWidth={1920}
        videoHeight={1080}
        fps={30}
        cursorSize={40}
      />
    );

    const cursorSvg = container.querySelector('svg');
    expect(cursorSvg).toHaveAttribute('width', '40');
    expect(cursorSvg).toHaveAttribute('height', '40');
  });

  it('handles empty cursor data gracefully', () => {
    const emptyCursorData: CursorTrackData = {
      id: 'empty-track',
      mouseData: [],
      metadata: {
        startTime: 0,
        endTime: 0,
        duration: 0,
        recordingDate: '2024-01-01T00:00:00.000Z',
      },
    };

    const { container } = render(
      <CursorOverlay
        cursorData={emptyCursorData}
        videoWidth={1920}
        videoHeight={1080}
        fps={30}
      />
    );

    // Should render container but no cursor elements
    expect(container.firstChild).toBeInTheDocument();
    const cursorElements = container.querySelectorAll('svg, path');
    expect(cursorElements).toHaveLength(0);
  });

  it('normalizes coordinates correctly for different video sizes', () => {
    mockUseCurrentFrame.mockReturnValue(6);
    
    // Test with smaller video dimensions
    const { container } = render(
      <CursorOverlay
        cursorData={mockCursorData}
        videoWidth={960} // Half of assumed 1920
        videoHeight={540} // Half of assumed 1080
        fps={30}
      />
    );

    // Cursor should be positioned at half the original coordinates
    const cursorElement = container.querySelector('[style*="position: absolute"]');
    expect(cursorElement).toBeInTheDocument();
    
    // The exact positioning calculation depends on the normalization logic
    // This test ensures the component handles different video dimensions
  });
});
