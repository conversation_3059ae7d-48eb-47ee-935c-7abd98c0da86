class MouseTrackerRecorder {
    constructor() {
        this.isRecording = false;
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.mouseData = [];
        this.currentTab = null;
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadState();
    }

    initializeElements() {
        this.startBtn = document.getElementById('startRecording');
        this.stopBtn = document.getElementById('stopRecording');
        this.downloadBtn = document.getElementById('downloadRecording');
        this.refreshTargetBtn = document.getElementById('refreshTarget');
        this.testMouseTrackingBtn = document.getElementById('testMouseTracking');
        this.openTestPageBtn = document.getElementById('openTestPage');
        this.statusText = document.getElementById('recording-status');
        this.statusDot = document.getElementById('status-dot');
        this.resultsSection = document.getElementById('resultsSection');
        this.coordCount = document.getElementById('coordCount');
        this.clickCount = document.getElementById('clickCount');
        this.recordedVideo = document.getElementById('recordedVideo');
        this.mouseVisualization = document.getElementById('mouseVisualization');
        this.copyMouseDataBtn = document.getElementById('copyMouseData');
        this.tabInfoSection = document.getElementById('tabInfoSection');
        this.targetTabTitle = document.getElementById('targetTabTitle');
        this.targetTabUrl = document.getElementById('targetTabUrl');
        this.tabOptionTitle = document.getElementById('tabOptionTitle');
        this.debugInfo = document.getElementById('debugInfo');
        this.debugText = document.getElementById('debugText');

        // React integration elements
        this.openInEditorBtn = document.getElementById('openInEditor');
        this.reactAppUrlInput = document.getElementById('reactAppUrl');
    }

    setupEventListeners() {
        this.startBtn.addEventListener('click', () => this.startRecording());
        this.stopBtn.addEventListener('click', () => this.stopRecording());
        this.downloadBtn.addEventListener('click', () => this.downloadRecording());
        this.refreshTargetBtn.addEventListener('click', () => this.updateTargetTabInfo());
        this.testMouseTrackingBtn.addEventListener('click', () => this.testMouseTracking());
        this.openTestPageBtn.addEventListener('click', () => this.openTestPage());
        this.copyMouseDataBtn.addEventListener('click', () => this.copyMouseData());

        // React integration event listeners
        if (this.openInEditorBtn) {
            this.openInEditorBtn.addEventListener('click', () => this.openInReactEditor());
        }

        // Listen for recording type changes
        document.querySelectorAll('input[name="recordingType"]').forEach(radio => {
            radio.addEventListener('change', () => this.updateTargetTabInfo());
        });

        // Listen for mouse tracking checkbox changes
        document.getElementById('enableMouseTracking').addEventListener('change', () => this.updateTargetTabInfo());

        // Listen for messages from background script
        chrome.runtime.onMessage.addListener((msg) => {
            console.log('Control page received message:', msg);
            if (msg.origin === 'background') {
                this.handleMouseData(msg.content, msg.metadata);
            }
        });
    }

    async loadState() {
        const result = await chrome.storage.sync.get(['isRecording', 'reactAppUrl']);
        if (result.isRecording) {
            this.updateUIState(true);
        }

        // Load React app URL
        if (result.reactAppUrl && this.reactAppUrlInput) {
            this.reactAppUrlInput.value = result.reactAppUrl;
        }

        // Update target tab info on load
        await this.updateTargetTabInfo();
    }

    async getTargetTab() {
        // Get all tabs
        const tabs = await chrome.tabs.query({});

        // Find a suitable tab (not extension pages, not chrome:// pages)
        const suitableTabs = tabs.filter(tab =>
            (tab.url.startsWith('http://') || tab.url.startsWith('https://')) &&
            !tab.url.includes('chrome-extension://')
        );

        if (suitableTabs.length === 0) {
            return null;
        }

        // Don't use the active tab if it's the control page
        const controlUrl = chrome.runtime.getURL('control.html');
        const nonControlTabs = suitableTabs.filter(tab => tab.url !== controlUrl);

        if (nonControlTabs.length === 0) {
            return null;
        }

        // Prefer the most recently used suitable tab
        const sortedTabs = nonControlTabs.sort((a, b) => b.lastAccessed - a.lastAccessed);
        return sortedTabs[0];
    }

    async updateTargetTabInfo() {
        // Always get target tab info to update the option title
        const targetTab = await this.getTargetTab();

        // Update the tab option title
        if (targetTab) {
            this.tabOptionTitle.textContent = targetTab.title || 'Untitled';

            // Update debug info
            this.debugInfo.style.display = 'block';
            this.debugText.textContent = `Target tab ready: ${targetTab.title}`;
        } else {
            this.tabOptionTitle.textContent = 'No suitable tab found';

            // Update debug info
            this.debugInfo.style.display = 'block';
            this.debugText.textContent = 'No suitable tab found - open a web page first';
        }

        // Hide the target tab info section since we're showing the name in the option card
        this.tabInfoSection.style.display = 'none';
    }

    async testMouseTracking() {
        try {
            const targetTab = await this.getTargetTab();
            if (!targetTab) {
                alert('Please open a web page (http:// or https://) in another tab for mouse tracking.');
                return;
            }

            this.currentTab = targetTab;
            this.debugInfo.style.display = 'block';
            this.debugText.textContent = 'Starting mouse tracking test...';

            await this.startMouseTracking();

            // Set a timeout to stop tracking and show results
            setTimeout(async () => {
                await this.stopMouseTracking();

                // Request mouse data from background
                chrome.runtime.sendMessage({
                    origin: 'control',
                    content: { action: 'stop' }
                });

                this.debugText.textContent = 'Test completed. Check console for results.';
            }, 10000); // Test for 10 seconds

        } catch (error) {
            console.error('Error testing mouse tracking:', error);
            this.debugText.textContent = `Test error: ${error.message}`;
        }
    }

    async openTestPage() {
        try {
            // Open a simple web page for testing
            // Using httpbin.org which provides a simple HTML page
            const testUrl = 'https://httpbin.org/html';
            await chrome.tabs.create({ url: testUrl });

            // Update target tab info after a short delay
            setTimeout(() => {
                this.updateTargetTabInfo();
            }, 1000);

            this.debugText.textContent = 'Test page opened. You can now start recording and mouse tracking.';

        } catch (error) {
            console.error('Error opening test page:', error);
            this.debugText.textContent = `Error opening test page: ${error.message}`;
        }
    }

    async startRecording() {
        try {
            const recordingType = document.querySelector('input[name="recordingType"]:checked').value;
            const enableMouseTracking = document.getElementById('enableMouseTracking').checked;

            // For tab recording and mouse tracking, we need a valid web page
            if (recordingType === 'tab' || enableMouseTracking) {
                // Get a suitable tab for recording/tracking
                const targetTab = await this.getTargetTab();
                if (!targetTab) {
                    const message = recordingType === 'tab'
                        ? 'Please open a web page (http:// or https://) in another tab for tab recording.'
                        : 'Please open a web page (http:// or https://) in another tab for mouse tracking.';
                    alert(message + '\n\nNote: The extension cannot record its own control page or Chrome internal pages.');
                    return;
                }
                this.currentTab = targetTab;

                // Update debug info
                this.debugText.textContent = `Using target tab: ${targetTab.title}`;
            }

            // Notify background script that recording is starting
            chrome.runtime.sendMessage({
                origin: 'control',
                content: { action: 'start' }
            });

            // Start screen recording
            await this.startScreenRecording(recordingType);

            // Start mouse tracking if enabled and we have a valid tab
            if (enableMouseTracking && this.currentTab) {
                await this.startMouseTracking();
            }

            this.isRecording = true;
            await chrome.storage.sync.set({ isRecording: true });
            this.updateUIState(true);

        } catch (error) {
            console.error('Error starting recording:', error);

            // Provide more helpful error messages
            let errorMessage = error.message;
            if (errorMessage.includes('activeTab permission')) {
                errorMessage = 'Cannot record this page. Please switch to a regular web page (http:// or https://) and try again.';
            } else if (errorMessage.includes('Chrome pages cannot be captured')) {
                errorMessage = 'Cannot record Chrome internal pages. Please open a regular web page and try again.';
            }

            alert(`Failed to start recording: ${errorMessage}`);
            this.debugText.textContent = `Error: ${errorMessage}`;
        }
    }

    async startScreenRecording(type) {
        try {
            let stream;

            if (type === 'tab') {
                // Make sure we have a valid tab and switch to it
                if (!this.currentTab) {
                    throw new Error('No suitable tab found for recording');
                }

                // Switch to the target tab first
                await chrome.tabs.update(this.currentTab.id, { active: true });
                await chrome.windows.update(this.currentTab.windowId, { focused: true });

                // Wait a moment for the tab to become active
                await new Promise(resolve => setTimeout(resolve, 500));

                // Record the tab using tabCapture
                stream = await new Promise((resolve, reject) => {
                    chrome.tabCapture.capture({
                        audio: true,
                        video: true
                    }, (capturedStream) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else if (capturedStream) {
                            resolve(capturedStream);
                        } else {
                            reject(new Error('Failed to capture tab'));
                        }
                    });
                });
            } else {
                // Record window or screen using desktopCapture
                const sources = type === 'screen' ? ['screen'] : ['window'];
                const streamId = await new Promise((resolve, reject) => {
                    chrome.desktopCapture.chooseDesktopMedia(sources, (streamId) => {
                        if (streamId) {
                            resolve(streamId);
                        } else {
                            reject(new Error('User cancelled screen selection'));
                        }
                    });
                });

                // Get media stream using the streamId
                stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        mandatory: {
                            chromeMediaSource: 'desktop',
                            chromeMediaSourceId: streamId
                        }
                    },
                    video: {
                        mandatory: {
                            chromeMediaSource: 'desktop',
                            chromeMediaSourceId: streamId
                        }
                    }
                });
            }

            // Setup MediaRecorder
            this.recordedChunks = [];

            // Try different codecs based on browser support
            let mimeType = 'video/webm;codecs=vp9';
            if (!MediaRecorder.isTypeSupported(mimeType)) {
                mimeType = 'video/webm;codecs=vp8';
                if (!MediaRecorder.isTypeSupported(mimeType)) {
                    mimeType = 'video/webm';
                }
            }

            this.mediaRecorder = new MediaRecorder(stream, { mimeType });

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
                const url = URL.createObjectURL(blob);
                this.recordedVideo.src = url;
                this.downloadBtn.disabled = false;

                // Stop all tracks to free up resources
                stream.getTracks().forEach(track => track.stop());
            };

            this.mediaRecorder.start();

        } catch (error) {
            console.error('Screen recording error:', error);
            throw error;
        }
    }

    async startMouseTracking() {
        try {
            // Clear previous mouse data
            this.mouseData = [];

            // Show debug info
            this.debugInfo.style.display = 'block';
            this.debugText.textContent = 'Injecting mouse tracker...';

            console.log('Starting mouse tracking for tab:', this.currentTab.id, this.currentTab.url);

            // Inject the content script file
            await chrome.scripting.executeScript({
                target: { tabId: this.currentTab.id },
                files: ['content-script.js']
            });

            console.log('Content script injected successfully');
            this.debugText.textContent = 'Content script injected, starting tracking...';

            // Wait a moment for the script to initialize
            await new Promise(resolve => setTimeout(resolve, 200));

            // Send message to start tracking
            const response = await chrome.tabs.sendMessage(this.currentTab.id, { action: 'startMouseTracking' });
            console.log('Start tracking response:', response);

            this.debugText.textContent = 'Mouse tracker active! Move your mouse on the target tab.';

            // Set badge to indicate recording
            await chrome.action.setBadgeText({ text: 'REC' });
            await chrome.action.setBadgeBackgroundColor({ color: '#F00' });

        } catch (error) {
            console.error('Error starting mouse tracking:', error);
            this.debugText.textContent = `Error: ${error.message}`;

            // Try to provide more helpful error messages
            if (error.message.includes('Cannot access contents of url')) {
                this.debugText.textContent = 'Error: Cannot access this page. Try a regular website (http/https).';
            } else if (error.message.includes('No tab with id')) {
                this.debugText.textContent = 'Error: Target tab not found. Please refresh and try again.';
            }

            throw error;
        }
    }



    async stopRecording() {
        try {
            // Stop screen recording
            if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
                this.mediaRecorder.stop();
            }

            // Stop mouse tracking
            await this.stopMouseTracking();

            this.isRecording = false;
            await chrome.storage.sync.set({ isRecording: false });
            this.updateUIState(false);

            // Request mouse data from background
            chrome.runtime.sendMessage({
                origin: 'control',
                content: { action: 'stop' }
            });

        } catch (error) {
            console.error('Error stopping recording:', error);
        }
    }

    async stopMouseTracking() {
        try {
            if (this.currentTab) {
                // Send message to stop tracking
                await chrome.tabs.sendMessage(this.currentTab.id, { action: 'stopMouseTracking' });
            }

            // Clear badge
            await chrome.action.setBadgeText({ text: '' });
            await chrome.action.setBadgeBackgroundColor({ color: '#FFF' });

        } catch (error) {
            console.error('Error stopping mouse tracking:', error);
            // Don't throw here as this is cleanup
        }
    }

    updateUIState(recording) {
        if (recording) {
            this.statusText.textContent = 'Recording...';
            this.statusDot.classList.add('recording');
            this.startBtn.disabled = true;
            this.stopBtn.disabled = false;
        } else {
            this.statusText.textContent = 'Ready';
            this.statusDot.classList.remove('recording');
            this.startBtn.disabled = false;
            this.stopBtn.disabled = true;
            this.resultsSection.style.display = 'block';

            // Show React integration button if we have recording data
            if (this.openInEditorBtn && this.recordedChunks.length > 0) {
                this.openInEditorBtn.style.display = 'block';
            }
        }
    }

    handleMouseData(data, metadata) {
        console.log('Handling mouse data:', data.length, 'points');
        this.mouseData = data;
        this.recordingMetadata = metadata;
        this.debugText.textContent = `Received ${data.length} mouse points`;
        this.updateMouseStats();
        this.visualizeMouseData();
    }

    updateMouseStats() {
        const clicks = this.mouseData.filter(item => item.action === 'click').length;
        this.coordCount.textContent = this.mouseData.length;
        this.clickCount.textContent = clicks;
    }

    async visualizeMouseData() {
        if (this.mouseData.length === 0) return;

        // Create canvas for visualization
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 300;
        const ctx = canvas.getContext('2d');

        // Clear previous visualization
        this.mouseVisualization.innerHTML = '';

        // Get dimensions from the tracked tab if available
        let maxX = 1920, maxY = 1080; // Default dimensions

        try {
            if (this.currentTab) {
                const results = await chrome.scripting.executeScript({
                    target: { tabId: this.currentTab.id },
                    function: () => ({
                        width: window.innerWidth,
                        height: window.innerHeight
                    })
                });
                if (results && results[0] && results[0].result) {
                    maxX = results[0].result.width;
                    maxY = results[0].result.height;
                }
            }
        } catch (error) {
            console.warn('Could not get tab dimensions, using defaults');
        }

        // Draw mouse path
        ctx.strokeStyle = '#667eea';
        ctx.lineWidth = 2;
        ctx.beginPath();

        this.mouseData.forEach((item, index) => {
            const x = (item.coords.x / maxX) * canvas.width;
            const y = (item.coords.y / maxY) * canvas.height;

            if (index === 0) {
                ctx.moveTo(x, y);
            } else if (item.action === 'click') {
                // Draw click indicator
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(x - 3, y - 3, 6, 6);
            } else {
                ctx.lineTo(x, y);
                ctx.stroke();
            }
        });

        this.mouseVisualization.appendChild(canvas);
    }

    downloadRecording() {
        if (this.recordedChunks.length === 0) return;

        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `recording-${new Date().toISOString().slice(0, 19)}.webm`;
        a.click();
        URL.revokeObjectURL(url);
    }

    copyMouseData() {
        const data = {
            mouseActions: this.mouseData,
            recordingInfo: {
                totalCoordinates: this.mouseData.length,
                totalClicks: this.mouseData.filter(item => item.action === 'click').length,
                timestamp: new Date().toISOString(),
                duration: this.recordingMetadata ? this.recordingMetadata.endTime - this.recordingMetadata.startTime : 0
            }
        };

        navigator.clipboard.writeText(JSON.stringify(data, null, 2)).then(() => {
            // Show feedback
            const originalText = this.copyMouseDataBtn.textContent;
            this.copyMouseDataBtn.textContent = 'Copied!';
            setTimeout(() => {
                this.copyMouseDataBtn.textContent = originalText;
            }, 2000);
        });
    }

    async openInReactEditor() {
        try {
            if (this.recordedChunks.length === 0) {
                alert('No recording available. Please record something first.');
                return;
            }

            // Get React app URL from input or storage
            let reactAppUrl = this.reactAppUrlInput ? this.reactAppUrlInput.value : 'http://localhost:3000';
            if (!reactAppUrl) {
                reactAppUrl = 'http://localhost:3000';
            }

            // Save React app URL to storage
            await chrome.storage.sync.set({ reactAppUrl: reactAppUrl });

            // Create video blob
            const videoBlob = new Blob(this.recordedChunks, { type: 'video/webm' });

            // Send message to background script to handle the React integration
            const response = await chrome.runtime.sendMessage({
                origin: 'control',
                content: {
                    action: 'openReactEditor',
                    videoBlob: videoBlob,
                    reactAppUrl: reactAppUrl
                }
            });

            if (response.opened) {
                // Show feedback
                const originalText = this.openInEditorBtn.textContent;
                this.openInEditorBtn.textContent = 'Opened!';
                setTimeout(() => {
                    this.openInEditorBtn.textContent = originalText;
                }, 2000);

                this.debugText.textContent = 'Recording opened in React Video Editor!';
            }

        } catch (error) {
            console.error('Error opening in React editor:', error);
            alert(`Failed to open in React editor: ${error.message}`);
        }
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new MouseTrackerRecorder();
});
