import React, { useEffect, useState } from 'react';
import { useChromeExtensionIntegration } from '../hooks/use-chrome-extension-integration';
import { useCursorStore } from '../store/use-cursor-store';
import { useLocalVideosStore } from '../store/use-local-videos-store';
import { Button } from '@/components/ui/button';
import { AlertCircle, CheckCircle, Download, X } from 'lucide-react';

interface ChromeExtensionIntegrationProps {
  onClose?: () => void;
}

export const ChromeExtensionIntegration: React.FC<ChromeExtensionIntegrationProps> = ({
  onClose
}) => {
  const {
    isConnected,
    recordingData,
    error,
    getCursorDataForVideo,
    clearCursorData
  } = useChromeExtensionIntegration();

  const { actions: cursorActions } = useCursorStore();
  const { videos, actions: videoActions } = useLocalVideosStore();
  const [showInstructions, setShowInstructions] = useState(false);
  const [processingVideo, setProcessingVideo] = useState(false);

  useEffect(() => {
    if (recordingData && recordingData.mouseData.length > 0) {
      setShowInstructions(true);
    }
  }, [recordingData]);

  const handleVideoFileLoad = async (file: File) => {
    if (!recordingData) return;

    try {
      setProcessingVideo(true);

      // Add video to the editor
      const video = await videoActions.addVideo(file);

      // Add cursor track for this video
      const cursorTrackId = cursorActions.addCursorTrack(
        video.id,
        {
          id: recordingData.sessionId,
          mouseData: recordingData.mouseData,
          metadata: recordingData.metadata,
          videoId: video.id
        },
        `Cursor Track - ${file.name}`
      );

      console.log('Video and cursor track added successfully:', {
        videoId: video.id,
        cursorTrackId
      });

      // Clear the recording data
      clearCursorData(recordingData.sessionId);
      setShowInstructions(false);

    } catch (error) {
      console.error('Error processing video file:', error);
    } finally {
      setProcessingVideo(false);
    }
  };

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleVideoFileLoad(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file && file.type.startsWith('video/')) {
      handleVideoFileLoad(file);
    }
  };

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-red-400 mt-0.5 mr-3" />
          <div className="flex-1">
            <h3 className="text-sm font-medium text-red-800">
              Chrome Extension Error
            </h3>
            <p className="text-sm text-red-700 mt-1">{error}</p>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="ml-3 text-red-400 hover:text-red-600"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    );
  }

  if (isConnected && recordingData) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <div className="flex items-start">
          <CheckCircle className="h-5 w-5 text-blue-400 mt-0.5 mr-3" />
          <div className="flex-1">
            <h3 className="text-sm font-medium text-blue-800">
              Screen Recording Loaded
            </h3>
            <p className="text-sm text-blue-700 mt-1">
              Recording with {recordingData.mouseData.length} cursor points loaded.
              Duration: {Math.round(recordingData.metadata.duration / 1000)}s
            </p>
            
            {showInstructions && (
              <div className="mt-3 p-3 bg-white rounded border">
                <p className="text-sm text-gray-700 mb-3">
                  <strong>Load your video file:</strong> The video "{recordingData.videoFileName}" 
                  should have been downloaded. Please load it to see the cursor overlay.
                </p>
                
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors"
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                >
                  <Download className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600 mb-2">
                    Drag and drop your video file here, or
                  </p>
                  <label className="cursor-pointer">
                    <input
                      type="file"
                      accept="video/*"
                      onChange={handleFileInputChange}
                      className="hidden"
                      disabled={processingVideo}
                    />
                    <Button 
                      variant="outline" 
                      size="sm"
                      disabled={processingVideo}
                    >
                      {processingVideo ? 'Processing...' : 'Select Video File'}
                    </Button>
                  </label>
                </div>
              </div>
            )}
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="ml-3 text-blue-400 hover:text-blue-600"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    );
  }

  return null;
};

export default ChromeExtensionIntegration;
