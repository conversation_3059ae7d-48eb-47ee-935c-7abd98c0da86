import { create } from 'zustand';
import { CursorTrackData } from '../hooks/use-chrome-extension-integration';

export interface CursorTrack {
  id: string;
  name: string;
  videoId: string; // Associated video ID
  cursorData: CursorTrackData;
  isVisible: boolean;
  settings: {
    showPath: boolean;
    cursorSize: number;
    pathColor: string;
    clickColor: string;
    pathDuration: number; // ms
    clickDuration: number; // ms
  };
}

interface CursorState {
  cursorTracks: CursorTrack[];
  activeCursorTrackId: string | null;
  actions: {
    addCursorTrack: (videoId: string, cursorData: CursorTrackData, name?: string) => string;
    removeCursorTrack: (id: string) => void;
    updateCursorTrack: (id: string, updates: Partial<CursorTrack>) => void;
    setActiveCursorTrack: (id: string | null) => void;
    getCursorTrackByVideoId: (videoId: string) => CursorTrack | undefined;
    toggleCursorTrackVisibility: (id: string) => void;
    updateCursorSettings: (id: string, settings: Partial<CursorTrack['settings']>) => void;
    clearAll: () => void;
  };
}

const DEFAULT_CURSOR_SETTINGS: CursorTrack['settings'] = {
  showPath: true,
  cursorSize: 20,
  pathColor: '#667eea',
  clickColor: '#e74c3c',
  pathDuration: 2000,
  clickDuration: 500
};

export const useCursorStore = create<CursorState>((set, get) => ({
  cursorTracks: [],
  activeCursorTrackId: null,

  actions: {
    addCursorTrack: (videoId: string, cursorData: CursorTrackData, name?: string) => {
      const id = `cursor_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const trackName = name || `Cursor Track ${get().cursorTracks.length + 1}`;

      const newTrack: CursorTrack = {
        id,
        name: trackName,
        videoId,
        cursorData,
        isVisible: true,
        settings: { ...DEFAULT_CURSOR_SETTINGS }
      };

      set((state) => ({
        cursorTracks: [...state.cursorTracks, newTrack],
        activeCursorTrackId: id
      }));

      return id;
    },

    removeCursorTrack: (id: string) => {
      set((state) => ({
        cursorTracks: state.cursorTracks.filter(track => track.id !== id),
        activeCursorTrackId: state.activeCursorTrackId === id ? null : state.activeCursorTrackId
      }));
    },

    updateCursorTrack: (id: string, updates: Partial<CursorTrack>) => {
      set((state) => ({
        cursorTracks: state.cursorTracks.map(track =>
          track.id === id ? { ...track, ...updates } : track
        )
      }));
    },

    setActiveCursorTrack: (id: string | null) => {
      set({ activeCursorTrackId: id });
    },

    getCursorTrackByVideoId: (videoId: string) => {
      return get().cursorTracks.find(track => track.videoId === videoId);
    },

    toggleCursorTrackVisibility: (id: string) => {
      set((state) => ({
        cursorTracks: state.cursorTracks.map(track =>
          track.id === id ? { ...track, isVisible: !track.isVisible } : track
        )
      }));
    },

    updateCursorSettings: (id: string, settings: Partial<CursorTrack['settings']>) => {
      set((state) => ({
        cursorTracks: state.cursorTracks.map(track =>
          track.id === id 
            ? { ...track, settings: { ...track.settings, ...settings } }
            : track
        )
      }));
    },

    clearAll: () => {
      set({
        cursorTracks: [],
        activeCursorTrackId: null
      });
    }
  }
}));
