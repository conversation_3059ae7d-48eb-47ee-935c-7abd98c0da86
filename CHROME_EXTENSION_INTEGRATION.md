# Chrome Extension to React Video Editor Integration

This document outlines the complete implementation for integrating Chrome extension screen recording functionality with cursor tracking into the React video editor application.

## Overview

The integration enables users to:
1. Record screen content with mouse cursor tracking via Chrome extension
2. Automatically transfer recorded video and cursor data to the React video editor
3. View synchronized cursor overlays on the recorded video
4. Customize cursor appearance and behavior

## Architecture

### Communication Flow
```
Chrome Extension → Screen Recording + Cursor Tracking → React Video Editor
                ↓
            File Transfer + Data Sync
                ↓
        Video Player with Cursor Overlay
```

### Data Structures

#### Mouse Data Format
```json
{
  "action": "move" | "click",
  "coords": { "x": number, "y": number },
  "timestamp": number
}
```

#### Recording Metadata
```json
{
  "sessionId": "recording_timestamp",
  "mouseData": MouseData[],
  "metadata": {
    "startTime": number,
    "endTime": number,
    "duration": number,
    "recordingDate": string
  },
  "videoFileName": string
}
```

## Implementation Details

### Chrome Extension Enhancements

#### 1. Enhanced Background Script (`background.js`)
- **Recording Metadata Tracking**: Stores start/end times and session data
- **Timestamped Mouse Data**: Adds relative timestamps to cursor movements
- **React App Integration**: Opens React editor with session parameters
- **File Download Management**: Automatically downloads recorded video

#### 2. Enhanced Control Interface (`control.js`)
- **React Integration Settings**: Configurable React app URL
- **Automatic Editor Launch**: One-click integration with video editor
- **Session Management**: Unique session IDs for data correlation

#### 3. Updated Manifest (`manifest.json`)
- **Downloads Permission**: For automatic file downloads
- **External Connectivity**: Allows communication with React app
- **Host Permissions**: Supports localhost development

### React Video Editor Integration

#### 1. Chrome Extension Hook (`use-chrome-extension-integration.ts`)
- **URL Parameter Detection**: Automatically detects recording sessions
- **Extension Communication**: Connects to Chrome extension for data retrieval
- **File Association**: Links downloaded videos with cursor data

#### 2. Cursor Overlay Component (`cursor-overlay.tsx`)
- **Real-time Rendering**: Synchronized cursor position with video timeline
- **Path Visualization**: Shows mouse movement trails
- **Click Indicators**: Animated click feedback
- **Coordinate Normalization**: Adapts to different screen resolutions

#### 3. Cursor Store (`use-cursor-store.ts`)
- **Track Management**: Multiple cursor tracks per video
- **Settings Control**: Customizable appearance and behavior
- **Visibility Toggle**: Show/hide cursor overlays

#### 4. Integration Component (`chrome-extension-integration.tsx`)
- **Status Notifications**: Shows connection and loading states
- **File Drop Zone**: Drag-and-drop video loading
- **Error Handling**: User-friendly error messages

## File Structure

```
Chrome Extension:
├── manifest.json (updated)
├── background.js (enhanced)
├── control.js (enhanced)
├── control.html (updated UI)
└── content-script.js (existing)

React Video Editor:
├── src/features/editor/
│   ├── hooks/
│   │   └── use-chrome-extension-integration.ts
│   ├── components/
│   │   ├── cursor-overlay.tsx
│   │   └── chrome-extension-integration.tsx
│   ├── store/
│   │   └── use-cursor-store.ts
│   └── menu-item/
│       └── cursor-tracks.tsx
├── src/remotion/
│   └── VideoEditorComposition.tsx (updated)
└── CHROME_EXTENSION_INTEGRATION.md
```

## Usage Workflow

### 1. Recording Phase
1. Open Chrome extension control panel
2. Configure React app URL (default: http://localhost:3000)
3. Select recording type (tab/window/screen)
4. Enable mouse tracking
5. Start recording
6. Perform actions on screen
7. Stop recording

### 2. Integration Phase
1. Extension automatically downloads video file
2. Extension opens React video editor with session parameter
3. React app detects session and loads cursor data
4. User drags downloaded video file into editor
5. Cursor track is automatically associated with video

### 3. Editing Phase
1. Video plays with synchronized cursor overlay
2. Customize cursor appearance in sidebar
3. Toggle cursor visibility
4. Export final video with cursor overlay

## Configuration

### Chrome Extension Settings
- **React App URL**: Configure target React application URL
- **Recording Quality**: Video codec and quality settings
- **Mouse Tracking**: Enable/disable cursor tracking

### React Editor Settings
- **Cursor Size**: 10-50px cursor size
- **Path Color**: Customizable trail color
- **Click Color**: Click indicator color
- **Path Duration**: Trail visibility duration (500-5000ms)
- **Click Duration**: Click indicator duration

## Technical Considerations

### File Formats
- **Video**: WebM format with VP9/VP8 codec
- **Data**: JSON format for cursor tracking data
- **Storage**: Chrome extension local storage for session data

### Performance
- **Mouse Throttling**: 50ms throttle for mouse movement events
- **Memory Management**: Automatic cleanup of session data
- **Rendering Optimization**: Efficient cursor overlay rendering

### Browser Compatibility
- **Chrome**: Full support with Manifest V3
- **Edge**: Compatible with Chrome extension APIs
- **Firefox**: Requires adaptation for WebExtensions

## Error Handling

### Common Issues
1. **Extension Not Found**: Install Chrome extension
2. **Permission Denied**: Grant screen recording permissions
3. **File Not Found**: Ensure video file is downloaded
4. **Data Mismatch**: Check session ID correlation

### Debugging
- Enable Chrome extension developer mode
- Check browser console for error messages
- Verify React app URL configuration
- Test with simple recording first

## Future Enhancements

### Planned Features
1. **Multi-cursor Support**: Track multiple cursors simultaneously
2. **Keyboard Tracking**: Record keyboard interactions
3. **Window Focus**: Track active window changes
4. **Audio Sync**: Synchronize audio with cursor events
5. **Cloud Storage**: Store recordings in cloud services

### API Extensions
1. **Webhook Integration**: Real-time data streaming
2. **REST API**: Programmatic access to recordings
3. **WebSocket**: Live cursor streaming
4. **Plugin System**: Extensible cursor effects

## Security Considerations

### Data Privacy
- All recording data stays local by default
- No external servers involved in basic workflow
- User controls all data sharing

### Permissions
- Minimal required permissions
- Clear permission explanations
- User consent for each recording session

## Testing

### Unit Tests

#### Cursor Overlay Component Tests
```bash
npm test src/features/editor/components/__tests__/cursor-overlay.test.tsx
```

Tests cover:
- Cursor positioning accuracy
- Path rendering with different settings
- Click indicator animations
- Coordinate normalization
- Performance with large datasets

#### Chrome Extension Integration Tests
```bash
npm test src/features/editor/hooks/__tests__/use-chrome-extension-integration.test.ts
```

Tests cover:
- URL parameter detection
- Extension communication
- Data retrieval and storage
- Error handling scenarios

### Integration Testing

#### End-to-End Workflow Test
1. **Setup**: Load Chrome extension in developer mode
2. **Record**: Create a test recording with mouse movements
3. **Transfer**: Verify automatic React app opening
4. **Load**: Test video file drag-and-drop
5. **Verify**: Check cursor overlay synchronization

#### Performance Testing
```javascript
// Example performance test
const testLargeCursorDataset = () => {
  const largeDataset = generateMouseData(10000); // 10k points
  const startTime = performance.now();

  render(<CursorOverlay cursorData={largeDataset} />);

  const endTime = performance.now();
  expect(endTime - startTime).toBeLessThan(100); // < 100ms
};
```

### Manual Testing Checklist

#### Chrome Extension
- [ ] Extension loads without errors
- [ ] Screen recording works for all modes (tab/window/screen)
- [ ] Mouse tracking captures movements and clicks
- [ ] React app URL configuration saves correctly
- [ ] Video download completes successfully

#### React Integration
- [ ] URL parameter detection works
- [ ] Extension connection establishes
- [ ] Recording data loads correctly
- [ ] Video file association works
- [ ] Cursor overlay renders accurately

#### Cursor Overlay
- [ ] Cursor position matches video content
- [ ] Path visualization shows correctly
- [ ] Click indicators animate properly
- [ ] Settings changes apply immediately
- [ ] Performance remains smooth with long recordings

## Example Implementation

### Basic Usage Example
```typescript
// In your React component
import { useChromeExtensionIntegration } from './hooks/use-chrome-extension-integration';
import { CursorOverlay } from './components/cursor-overlay';

const VideoPlayer = () => {
  const { recordingData, isConnected } = useChromeExtensionIntegration();

  return (
    <div className="video-container">
      <video src="recording.webm" />
      {recordingData && (
        <CursorOverlay
          cursorData={recordingData}
          videoWidth={1920}
          videoHeight={1080}
          fps={30}
        />
      )}
    </div>
  );
};
```

### Custom Cursor Settings
```typescript
const customCursorSettings = {
  showPath: true,
  cursorSize: 25,
  pathColor: '#ff6b6b',
  clickColor: '#4ecdc4',
  pathDuration: 3000,
  clickDuration: 750
};

<CursorOverlay
  cursorData={cursorData}
  {...customCursorSettings}
/>
```

### Advanced Integration
```typescript
// Custom hook for advanced cursor management
const useAdvancedCursorIntegration = () => {
  const { recordingData } = useChromeExtensionIntegration();
  const { cursorTracks, actions } = useCursorStore();

  const processRecording = useCallback(async (videoFile: File) => {
    if (recordingData) {
      const trackId = actions.addCursorTrack(
        videoFile.name,
        recordingData,
        `Recording - ${new Date().toLocaleString()}`
      );

      // Apply intelligent settings based on recording type
      const settings = analyzeRecordingPattern(recordingData.mouseData);
      actions.updateCursorSettings(trackId, settings);
    }
  }, [recordingData, actions]);

  return { processRecording };
};
```
