import { useEffect, useState } from 'react';
import { useLocalVideosStore } from '../store/use-local-videos-store';

export interface ChromeRecordingData {
  sessionId: string;
  mouseData: Array<{
    action: 'move' | 'click';
    coords: { x: number; y: number };
    timestamp: number;
  }>;
  metadata: {
    startTime: number;
    endTime: number;
    duration: number;
    recordingDate: string;
  };
  videoFileName: string;
}

export interface CursorTrackData {
  id: string;
  mouseData: ChromeRecordingData['mouseData'];
  metadata: ChromeRecordingData['metadata'];
  videoId?: string;
}

export const useChromeExtensionIntegration = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [recordingData, setRecordingData] = useState<ChromeRecordingData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { actions: videoActions } = useLocalVideosStore();

  useEffect(() => {
    // Check if we have a recording session parameter in URL
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get('recording');
    
    if (sessionId) {
      loadRecordingFromExtension(sessionId);
    }
  }, []);

  const loadRecordingFromExtension = async (sessionId: string) => {
    try {
      setError(null);
      
      // Connect to Chrome extension
      const extensionId = await detectChromeExtension();
      if (!extensionId) {
        throw new Error('Chrome extension not found. Please install the Mouse Tracker & Screen Recorder extension.');
      }

      // Request recording data from extension
      const port = chrome.runtime.connect(extensionId);
      setIsConnected(true);

      port.postMessage({
        action: 'getRecordingData',
        sessionId: sessionId
      });

      port.onMessage.addListener(async (response) => {
        if (response.success) {
          const data = response.data as ChromeRecordingData;
          setRecordingData(data);
          
          // Automatically load video file if available
          await loadVideoFromDownloads(data.videoFileName, data);
        } else {
          throw new Error(response.error || 'Failed to load recording data');
        }
      });

      port.onDisconnect.addListener(() => {
        setIsConnected(false);
      });

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      console.error('Chrome extension integration error:', err);
    }
  };

  const detectChromeExtension = async (): Promise<string | null> => {
    // Try to detect the extension by attempting to connect
    // This is a simplified approach - in production you might want to use a known extension ID
    try {
      // Check if chrome.runtime is available (we're in a Chrome context)
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        // For development, we'll assume the extension is available
        // In production, you'd want to use the actual extension ID
        return 'extension-id-placeholder';
      }
      return null;
    } catch {
      return null;
    }
  };

  const loadVideoFromDownloads = async (fileName: string, recordingData: ChromeRecordingData) => {
    try {
      // In a real implementation, you might:
      // 1. Use File System Access API to let user select the downloaded file
      // 2. Or implement a file drop zone for the downloaded video
      // 3. Or use a more sophisticated file transfer mechanism
      
      console.log('Video file should be downloaded as:', fileName);
      console.log('Recording data:', recordingData);
      
      // For now, we'll show instructions to the user
      showVideoLoadInstructions(fileName, recordingData);
      
    } catch (err) {
      console.error('Error loading video from downloads:', err);
    }
  };

  const showVideoLoadInstructions = (fileName: string, recordingData: ChromeRecordingData) => {
    // Create a notification or modal to guide user to load the video
    const message = `
      Your screen recording "${fileName}" has been downloaded.
      Please drag and drop the video file into the video editor to load it with cursor tracking data.
    `;
    
    // You could implement a toast notification or modal here
    console.log(message);
    
    // Store the cursor data for when the video is loaded
    if (recordingData.mouseData.length > 0) {
      storeCursorDataForVideo(recordingData);
    }
  };

  const storeCursorDataForVideo = (recordingData: ChromeRecordingData) => {
    // Store cursor data in localStorage or a store for later association with video
    const cursorData: CursorTrackData = {
      id: recordingData.sessionId,
      mouseData: recordingData.mouseData,
      metadata: recordingData.metadata
    };
    
    localStorage.setItem(`cursor_data_${recordingData.sessionId}`, JSON.stringify(cursorData));
    
    // Also store a mapping by filename for easier lookup
    localStorage.setItem(`cursor_data_file_${recordingData.videoFileName}`, JSON.stringify(cursorData));
  };

  const getCursorDataForVideo = (videoFileName: string): CursorTrackData | null => {
    try {
      const stored = localStorage.getItem(`cursor_data_file_${videoFileName}`);
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  };

  const clearCursorData = (sessionId: string) => {
    localStorage.removeItem(`cursor_data_${sessionId}`);
    // Also try to clear by filename if we have the recording data
    if (recordingData) {
      localStorage.removeItem(`cursor_data_file_${recordingData.videoFileName}`);
    }
  };

  return {
    isConnected,
    recordingData,
    error,
    loadRecordingFromExtension,
    getCursorDataForVideo,
    clearCursorData
  };
};
