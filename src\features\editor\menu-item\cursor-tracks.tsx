import React, { useState } from "react";
import { Scroll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { useCursorStore } from "../store/use-cursor-store";
import { useLocalVideosStore } from "../store/use-local-videos-store";
import { 
  <PERSON>, 
  EyeOff, 
  Trash2, 
  <PERSON>ting<PERSON>, 
  MousePointer,
  Palette,
  Sliders
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";

export const CursorTracks = () => {
  const { cursorTracks, activeCursorTrackId, actions } = useCursorStore();
  const { videos } = useLocalVideosStore();
  const [expandedSettings, setExpandedSettings] = useState<string | null>(null);

  const getVideoName = (videoId: string) => {
    const video = videos.find(v => v.id === videoId);
    return video?.name || 'Unknown Video';
  };

  const toggleSettings = (trackId: string) => {
    setExpandedSettings(expandedSettings === trackId ? null : trackId);
  };

  if (cursorTracks.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        <MousePointer className="h-8 w-8 mx-auto mb-2 opacity-50" />
        <p className="text-sm">No cursor tracks available</p>
        <p className="text-xs mt-1">
          Load a recording from the Chrome extension to see cursor tracks
        </p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-border/80">
        <h2 className="text-lg font-semibold">Cursor Tracks</h2>
        <p className="text-sm text-gray-600 mt-1">
          Manage cursor overlays for your recordings
        </p>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {cursorTracks.map((track) => (
            <div
              key={track.id}
              className={`border rounded-lg p-3 ${
                activeCursorTrackId === track.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200'
              }`}
            >
              {/* Track Header */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium truncate">{track.name}</h3>
                  <p className="text-xs text-gray-500 truncate">
                    {getVideoName(track.videoId)}
                  </p>
                </div>
                
                <div className="flex items-center space-x-1 ml-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => actions.toggleCursorTrackVisibility(track.id)}
                    className="h-8 w-8 p-0"
                  >
                    {track.isVisible ? (
                      <Eye className="h-4 w-4" />
                    ) : (
                      <EyeOff className="h-4 w-4 opacity-50" />
                    )}
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleSettings(track.id)}
                    className="h-8 w-8 p-0"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => actions.removeCursorTrack(track.id)}
                    className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Track Stats */}
              <div className="text-xs text-gray-500 mb-2">
                {track.cursorData.mouseData.length} points • 
                {track.cursorData.mouseData.filter(p => p.action === 'click').length} clicks • 
                {Math.round(track.cursorData.metadata.duration / 1000)}s
              </div>

              {/* Settings Panel */}
              {expandedSettings === track.id && (
                <div className="mt-3 pt-3 border-t border-gray-200 space-y-4">
                  {/* Cursor Size */}
                  <div>
                    <Label className="text-xs font-medium">Cursor Size</Label>
                    <div className="mt-1">
                      <Slider
                        value={[track.settings.cursorSize]}
                        onValueChange={([value]) =>
                          actions.updateCursorSettings(track.id, { cursorSize: value })
                        }
                        min={10}
                        max={50}
                        step={1}
                        className="w-full"
                      />
                      <div className="text-xs text-gray-500 mt-1">
                        {track.settings.cursorSize}px
                      </div>
                    </div>
                  </div>

                  {/* Show Path Toggle */}
                  <div className="flex items-center justify-between">
                    <Label className="text-xs font-medium">Show Path</Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        actions.updateCursorSettings(track.id, {
                          showPath: !track.settings.showPath
                        })
                      }
                      className="h-6 text-xs"
                    >
                      {track.settings.showPath ? 'On' : 'Off'}
                    </Button>
                  </div>

                  {/* Path Color */}
                  <div>
                    <Label className="text-xs font-medium">Path Color</Label>
                    <div className="mt-1 flex items-center space-x-2">
                      <input
                        type="color"
                        value={track.settings.pathColor}
                        onChange={(e) =>
                          actions.updateCursorSettings(track.id, {
                            pathColor: e.target.value
                          })
                        }
                        className="w-8 h-6 rounded border border-gray-300"
                      />
                      <Input
                        value={track.settings.pathColor}
                        onChange={(e) =>
                          actions.updateCursorSettings(track.id, {
                            pathColor: e.target.value
                          })
                        }
                        className="flex-1 h-6 text-xs"
                        placeholder="#667eea"
                      />
                    </div>
                  </div>

                  {/* Click Color */}
                  <div>
                    <Label className="text-xs font-medium">Click Color</Label>
                    <div className="mt-1 flex items-center space-x-2">
                      <input
                        type="color"
                        value={track.settings.clickColor}
                        onChange={(e) =>
                          actions.updateCursorSettings(track.id, {
                            clickColor: e.target.value
                          })
                        }
                        className="w-8 h-6 rounded border border-gray-300"
                      />
                      <Input
                        value={track.settings.clickColor}
                        onChange={(e) =>
                          actions.updateCursorSettings(track.id, {
                            clickColor: e.target.value
                          })
                        }
                        className="flex-1 h-6 text-xs"
                        placeholder="#e74c3c"
                      />
                    </div>
                  </div>

                  {/* Path Duration */}
                  <div>
                    <Label className="text-xs font-medium">Path Duration</Label>
                    <div className="mt-1">
                      <Slider
                        value={[track.settings.pathDuration]}
                        onValueChange={([value]) =>
                          actions.updateCursorSettings(track.id, { pathDuration: value })
                        }
                        min={500}
                        max={5000}
                        step={100}
                        className="w-full"
                      />
                      <div className="text-xs text-gray-500 mt-1">
                        {track.settings.pathDuration}ms
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};
