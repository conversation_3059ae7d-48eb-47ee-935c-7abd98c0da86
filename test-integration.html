<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome Extension Integration Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .data-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🎬 Chrome Extension Integration Test</h1>
    
    <div class="info">
        <strong>Extension ID:</strong> hbdaancoodgdmpleglaioekanhkbmjlo<br>
        <strong>Purpose:</strong> Test communication between Chrome extension and React app
    </div>

    <h2>Connection Test</h2>
    <button id="testConnection">Test Extension Connection</button>
    <div id="connectionStatus"></div>

    <h2>URL Parameter Test</h2>
    <div id="urlParams" class="data-display"></div>

    <h2>Recording Data Test</h2>
    <button id="testRecordingData" disabled>Load Recording Data</button>
    <div id="recordingDataStatus"></div>
    <div id="recordingDataDisplay" class="data-display" style="display: none;"></div>

    <h2>Local Storage Test</h2>
    <button id="testLocalStorage">Check Local Storage</button>
    <div id="localStorageDisplay" class="data-display" style="display: none;"></div>

    <h2>Instructions</h2>
    <ol>
        <li>Make sure the Chrome extension (ID: hbdaancoodgdmpleglaioekanhkbmjlo) is installed</li>
        <li>Click "Test Extension Connection" to verify communication</li>
        <li>Use the extension's "Test React Integration" button to send test data</li>
        <li>Refresh this page with the recording parameter to test data loading</li>
    </ol>

    <script>
        const EXTENSION_ID = 'hbdaancoodgdmpleglaioekanhkbmjlo';
        
        // Check URL parameters on load
        window.addEventListener('load', () => {
            const urlParams = new URLSearchParams(window.location.search);
            const recording = urlParams.get('recording');
            
            document.getElementById('urlParams').textContent = 
                `Current URL: ${window.location.href}\n` +
                `Recording parameter: ${recording || 'Not found'}\n` +
                `All parameters: ${JSON.stringify(Object.fromEntries(urlParams), null, 2)}`;
            
            if (recording) {
                document.getElementById('testRecordingData').disabled = false;
                document.getElementById('testRecordingData').textContent = `Load Recording: ${recording}`;
            }
        });

        // Test extension connection
        document.getElementById('testConnection').addEventListener('click', async () => {
            const statusDiv = document.getElementById('connectionStatus');
            
            try {
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    throw new Error('Chrome runtime not available');
                }
                
                const port = chrome.runtime.connect(EXTENSION_ID);
                
                port.onMessage.addListener((response) => {
                    statusDiv.innerHTML = `<div class="success">✅ Extension connected successfully!<br>Response: ${JSON.stringify(response)}</div>`;
                });
                
                port.onDisconnect.addListener(() => {
                    if (chrome.runtime.lastError) {
                        statusDiv.innerHTML = `<div class="error">❌ Connection failed: ${chrome.runtime.lastError.message}</div>`;
                    }
                });
                
                // Test message
                port.postMessage({ action: 'test', timestamp: Date.now() });
                
                setTimeout(() => {
                    if (!statusDiv.innerHTML.includes('connected successfully')) {
                        statusDiv.innerHTML = `<div class="warning">⚠️ Connection established but no response received</div>`;
                    }
                }, 2000);
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ Connection failed: ${error.message}</div>`;
            }
        });

        // Test recording data loading
        document.getElementById('testRecordingData').addEventListener('click', async () => {
            const statusDiv = document.getElementById('recordingDataStatus');
            const displayDiv = document.getElementById('recordingDataDisplay');
            
            const urlParams = new URLSearchParams(window.location.search);
            const sessionId = urlParams.get('recording');
            
            if (!sessionId) {
                statusDiv.innerHTML = `<div class="error">❌ No recording session ID in URL</div>`;
                return;
            }
            
            try {
                const port = chrome.runtime.connect(EXTENSION_ID);
                
                port.onMessage.addListener((response) => {
                    if (response.success) {
                        statusDiv.innerHTML = `<div class="success">✅ Recording data loaded successfully!</div>`;
                        displayDiv.textContent = JSON.stringify(response.data, null, 2);
                        displayDiv.style.display = 'block';
                    } else {
                        statusDiv.innerHTML = `<div class="error">❌ Failed to load recording data: ${response.error}</div>`;
                    }
                });
                
                port.postMessage({
                    action: 'getRecordingData',
                    sessionId: sessionId
                });
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        });

        // Test local storage
        document.getElementById('testLocalStorage').addEventListener('click', () => {
            const displayDiv = document.getElementById('localStorageDisplay');
            
            const storageData = {};
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key.includes('cursor_data') || key.includes('recording')) {
                    storageData[key] = localStorage.getItem(key);
                }
            }
            
            displayDiv.textContent = Object.keys(storageData).length > 0 
                ? JSON.stringify(storageData, null, 2)
                : 'No cursor/recording data found in localStorage';
            displayDiv.style.display = 'block';
        });
    </script>
</body>
</html>
