<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Mouse Tracker & Screen Recorder</title>
    <link rel="stylesheet" href="control.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Mouse Tracker & Screen Recorder</h1>
            <div class="status-indicator">
                <span id="recording-status">Ready</span>
                <div id="status-dot" class="status-dot"></div>
            </div>
            <div class="debug-info" id="debugInfo" style="display: none; font-size: 12px; color: #666; margin-top: 5px;">
                Debug: <span id="debugText">No data</span>
            </div>
        </header>

        <div class="main-content">
            <div class="left-column">
                <!-- Recording Type Selection -->
                <section class="recording-options">
                    <h2>Recording Options</h2>
                    <div class="option-group">
                        <label class="option-card">
                            <input type="radio" name="recordingType" value="tab" checked>
                            <div class="card-content">
                                <div class="card-icon">🗂️</div>
                                <div class="card-title" id="tabOptionTitle">Current Tab</div>
                                <div class="card-description">Record the active browser tab</div>
                            </div>
                        </label>

                        <label class="option-card">
                            <input type="radio" name="recordingType" value="window">
                            <div class="card-content">
                                <div class="card-icon">🪟</div>
                                <div class="card-title">Window</div>
                                <div class="card-description">Record a specific window</div>
                            </div>
                        </label>

                        <label class="option-card">
                            <input type="radio" name="recordingType" value="screen">
                            <div class="card-content">
                                <div class="card-icon">🖥️</div>
                                <div class="card-title">Entire Screen</div>
                                <div class="card-description">Record the full screen</div>
                            </div>
                        </label>
                    </div>
                    <p class="note" style="margin-top: 16px; font-size: 14px; color: #666; font-style: italic;">
                        Note: Mouse tracker zoom effects only work for the Tab option, not for Window and Screen. You can add your own area of effect zooms.
                    </p>
                </section>

                <!-- Mouse Tracking Options -->
                <section class="tracking-options">
                    <h2>Mouse Tracking</h2>
                    <div class="checkbox-group">
                        <label class="checkbox-option">
                            <input type="checkbox" id="enableMouseTracking" checked>
                            <span class="checkmark"></span>
                            <span class="label-text">Enable mouse tracking on selected tab</span>
                        </label>
                        <label class="checkbox-option">
                            <input type="checkbox" id="showMousePath">
                            <span class="checkmark"></span>
                            <span class="label-text">Show mouse path visualization</span>
                        </label>
                    </div>
                </section>

                <!-- Target Tab Info -->
                <section class="tab-info" id="tabInfoSection" style="display: none;">
                    <h2>Target Tab</h2>
                    <div class="tab-details">
                        <p><strong>Tab:</strong> <span id="targetTabTitle">Loading...</span></p>
                        <p><strong>URL:</strong> <span id="targetTabUrl">Loading...</span></p>
                        <p><small><em>Note: Cannot record extension pages or Chrome internal pages. Use regular web pages (http:// or https://).</em></small></p>
                    </div>
                </section>
            </div>

            <div class="right-column">
                <!-- Control Buttons -->
                <section class="controls-section">
                    <h2>Controls</h2>

                    <div class="primary-controls">
                        <button id="startRecording" class="btn btn-primary">
                            <span class="btn-icon">▶️</span>
                            Start Recording
                        </button>
                        <button id="stopRecording" class="btn btn-secondary" disabled>
                            <span class="btn-icon">⏹️</span>
                            Stop Recording
                        </button>
                    </div>

                    <div class="secondary-controls">
                        <button id="refreshTarget" class="btn btn-outline">
                            <span class="btn-icon">🔄</span>
                            Refresh Target Tab
                        </button>
                        <button id="testMouseTracking" class="btn btn-outline">
                            <span class="btn-icon">🖱️</span>
                            Test Mouse Tracking
                        </button>
                        <button id="openTestPage" class="btn btn-outline">
                            <span class="btn-icon">🧪</span>
                            Open Test Page
                        </button>
                        <button id="downloadRecording" class="btn btn-success" disabled>
                            <span class="btn-icon">💾</span>
                            Download Recording
                        </button>
                        <button id="openInEditor" class="btn btn-primary" disabled>
                            <span class="btn-icon">🎬</span>
                            Open in Video Editor
                        </button>
                    </div>

                    <!-- React Integration Settings -->
                    <div class="react-integration" style="margin-top: 20px; padding: 15px; border: 2px solid #007bff; border-radius: 8px; background: #e7f3ff;">
                        <h3 style="margin-top: 0; color: #007bff;">🎬 React Video Editor Integration</h3>
                        <div style="margin-bottom: 15px;">
                            <label for="reactAppUrl" style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">React App URL:</label>
                            <input type="url" id="reactAppUrl" placeholder="http://localhost:3000"
                                   style="width: 100%; padding: 10px; border: 2px solid #007bff; border-radius: 6px; font-size: 14px;">
                        </div>
                        <div style="background: #fff; padding: 10px; border-radius: 6px; border-left: 4px solid #007bff;">
                            <p style="font-size: 13px; color: #555; margin: 0; font-weight: 500;">
                                💡 <strong>How it works:</strong> After recording, the "Open in Video Editor" button will automatically transfer your recording with cursor data to the React video editor.
                            </p>
                        </div>

                        <!-- Test button for debugging -->
                        <div style="margin-top: 10px;">
                            <button id="testReactIntegration" class="btn btn-outline" style="width: 100%; font-size: 12px;">
                                <span class="btn-icon">🧪</span>
                                Test React Integration (No Recording Required)
                            </button>
                        </div>
                    </div>
                </section>

                <!-- Results Section -->
                <section class="results" id="resultsSection" style="display: none;">
                    <h2>Recording Results</h2>
                    <div class="results-content">
                        <div class="mouse-data">
                            <h3>Mouse Tracking Data</h3>
                            <div id="mouseStats">
                                <p>Coordinates tracked: <span id="coordCount">0</span></p>
                                <p>Clicks recorded: <span id="clickCount">0</span></p>
                            </div>
                            <button id="copyMouseData" class="btn btn-outline">Copy Mouse Data</button>
                            <div id="mouseVisualization"></div>
                        </div>
                        <div class="recording-data">
                            <h3>Screen Recording</h3>
                            <video id="recordedVideo" controls style="max-width: 100%; height: auto;"></video>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <script src="control.js"></script>
</body>
</html>
