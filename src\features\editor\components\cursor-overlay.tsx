import React, { useMemo } from 'react';
import { useCurrentFrame } from 'remotion';
import { CursorTrackData } from '../hooks/use-chrome-extension-integration';

interface CursorOverlayProps {
  cursorData: CursorTrackData;
  videoWidth: number;
  videoHeight: number;
  fps: number;
  showPath?: boolean;
  cursorSize?: number;
  pathColor?: string;
  clickColor?: string;
}

export const CursorOverlay: React.FC<CursorOverlayProps> = ({
  cursorData,
  videoWidth,
  videoHeight,
  fps,
  showPath = true,
  cursorSize = 20,
  pathColor = '#667eea',
  clickColor = '#e74c3c'
}) => {
  const frame = useCurrentFrame();
  const currentTimeMs = (frame / fps) * 1000;

  // Filter and process mouse data for current time
  const { currentCursor, pathPoints, recentClicks } = useMemo(() => {
    const timeWindow = 100; // ms tolerance for finding cursor position
    const pathDuration = 2000; // Show path for last 2 seconds
    const clickDuration = 500; // Show click indicators for 500ms

    // Find cursor position at current time
    const currentCursor = cursorData.mouseData.find(point => 
      Math.abs(point.timestamp - currentTimeMs) <= timeWindow
    );

    // Get path points for the last few seconds
    const pathStartTime = currentTimeMs - pathDuration;
    const pathPoints = cursorData.mouseData.filter(point => 
      point.timestamp >= pathStartTime && 
      point.timestamp <= currentTimeMs &&
      point.action === 'move'
    );

    // Get recent clicks
    const clickStartTime = currentTimeMs - clickDuration;
    const recentClicks = cursorData.mouseData.filter(point =>
      point.timestamp >= clickStartTime &&
      point.timestamp <= currentTimeMs &&
      point.action === 'click'
    );

    return { currentCursor, pathPoints, recentClicks };
  }, [cursorData.mouseData, currentTimeMs, fps]);

  // Convert coordinates to video space (assuming cursor data is in screen coordinates)
  const normalizeCoordinates = (x: number, y: number) => {
    // This assumes the cursor data was captured at a specific screen resolution
    // You might need to adjust this based on your recording setup
    const assumedScreenWidth = 1920; // Adjust based on typical recording resolution
    const assumedScreenHeight = 1080;
    
    return {
      x: (x / assumedScreenWidth) * videoWidth,
      y: (y / assumedScreenHeight) * videoHeight
    };
  };

  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: videoWidth,
        height: videoHeight,
        pointerEvents: 'none',
        zIndex: 10
      }}
    >
      {/* Mouse path */}
      {showPath && pathPoints.length > 1 && (
        <svg
          width={videoWidth}
          height={videoHeight}
          style={{ position: 'absolute', top: 0, left: 0 }}
        >
          <path
            d={pathPoints.map((point, index) => {
              const { x, y } = normalizeCoordinates(point.coords.x, point.coords.y);
              return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
            }).join(' ')}
            stroke={pathColor}
            strokeWidth="2"
            fill="none"
            opacity={0.7}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      )}

      {/* Click indicators */}
      {recentClicks.map((click, index) => {
        const { x, y } = normalizeCoordinates(click.coords.x, click.coords.y);
        const age = currentTimeMs - click.timestamp;
        const opacity = Math.max(0, 1 - (age / 500)); // Fade out over 500ms
        const scale = 1 + (age / 500) * 0.5; // Grow slightly as it fades

        return (
          <div
            key={`click-${click.timestamp}-${index}`}
            style={{
              position: 'absolute',
              left: x - (cursorSize * scale) / 2,
              top: y - (cursorSize * scale) / 2,
              width: cursorSize * scale,
              height: cursorSize * scale,
              borderRadius: '50%',
              backgroundColor: clickColor,
              opacity: opacity,
              transform: `scale(${scale})`,
              transition: 'all 0.1s ease-out'
            }}
          />
        );
      })}

      {/* Current cursor position */}
      {currentCursor && (
        <div
          style={{
            position: 'absolute',
            left: normalizeCoordinates(currentCursor.coords.x, currentCursor.coords.y).x - cursorSize / 2,
            top: normalizeCoordinates(currentCursor.coords.x, currentCursor.coords.y).y - cursorSize / 2,
            width: cursorSize,
            height: cursorSize,
            pointerEvents: 'none'
          }}
        >
          {/* Cursor arrow */}
          <svg
            width={cursorSize}
            height={cursorSize}
            viewBox="0 0 24 24"
            fill="none"
            style={{ filter: 'drop-shadow(1px 1px 2px rgba(0,0,0,0.5))' }}
          >
            <path
              d="M3 3L10.5 10.5L8 13L13 18L18 13L15.5 10.5L23 3L3 3Z"
              fill="white"
              stroke="black"
              strokeWidth="1"
            />
          </svg>
        </div>
      )}
    </div>
  );
};

export default CursorOverlay;
