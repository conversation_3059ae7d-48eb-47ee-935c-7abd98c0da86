var coord_list = [];
var recordingMetadata = {
  startTime: null,
  endTime: null,
  videoBlob: null,
  fileName: null
};

// Handle extension icon click - open control page
chrome.action.onClicked.addListener(async () => {
  // Create or focus the control page
  const url = chrome.runtime.getURL('control.html');

  // Check if control page is already open
  const tabs = await chrome.tabs.query({ url: url });

  if (tabs.length > 0) {
    // Focus existing tab
    chrome.tabs.update(tabs[0].id, { active: true });
    chrome.windows.update(tabs[0].windowId, { focused: true });
  } else {
    // Create new tab
    chrome.tabs.create({ url: url });
  }
});

// Handle messages from content scripts and control page
chrome.runtime.onMessage.addListener(async (msg, sender, sendResponse) => {
  console.log('Background received message:', msg, 'from:', sender);

  if (msg.origin === "content") {
    // Mouse tracking data from injected content script
    const timestampedData = {
      ...msg.content,
      timestamp: Date.now() - (recordingMetadata.startTime || Date.now())
    };
    coord_list.push(timestampedData);
    console.log('Added mouse data:', timestampedData, 'total points:', coord_list.length);

    // Send acknowledgment back to content script
    sendResponse({ received: true, totalPoints: coord_list.length });
  } else if (msg.origin === "control" && msg.content.action === "start") {
    // Recording started
    recordingMetadata.startTime = Date.now();
    coord_list = [];
    sendResponse({ started: true });
  } else if (msg.origin === "control" && msg.content.action === "stop") {
    // Control page requesting mouse data
    recordingMetadata.endTime = Date.now();
    console.log('Sending mouse data to control page, total points:', coord_list.length);

    // Find the control page tab
    const controlUrl = chrome.runtime.getURL('control.html');
    const tabs = await chrome.tabs.query({ url: controlUrl });

    if (tabs.length > 0) {
      // Send message to the control page tab
      chrome.tabs.sendMessage(tabs[0].id, {
        origin: "background",
        content: coord_list,
        metadata: recordingMetadata
      });
    }

    sendResponse({ sent: true });
  } else if (msg.origin === "control" && msg.content.action === "openReactEditor") {
    // Open React video editor with recording data
    await openReactVideoEditor(msg.content.videoBlob, coord_list, recordingMetadata);
    sendResponse({ opened: true });
  } else {
    // Other mouse tracking data (fallback)
    console.log('Fallback: adding mouse data:', msg.content);
    const timestampedData = {
      ...msg.content,
      timestamp: Date.now() - (recordingMetadata.startTime || Date.now())
    };
    coord_list.push(timestampedData);
    sendResponse({ received: true, totalPoints: coord_list.length });
  }

  return true; // Keep message channel open for async response
});

// Function to open React video editor with recording data
async function openReactVideoEditor(videoBlob, mouseData, metadata) {
  try {
    // Default React app URL (can be configured)
    const reactAppUrl = 'http://localhost:3000'; // Adjust this to your React app URL

    // Create a unique session ID for this recording
    const sessionId = 'recording_' + Date.now();

    // Store the recording data in chrome.storage for the React app to access
    const recordingData = {
      sessionId: sessionId,
      mouseData: mouseData,
      metadata: {
        ...metadata,
        duration: metadata.endTime - metadata.startTime,
        recordingDate: new Date().toISOString()
      },
      videoFileName: `screen_recording_${new Date().toISOString().slice(0, 19)}.webm`
    };

    // Store in chrome.storage.local (larger storage limit)
    await chrome.storage.local.set({
      [sessionId]: recordingData
    });

    // Create URL with session parameter
    const editorUrl = `${reactAppUrl}?recording=${sessionId}`;

    // Open React video editor in new tab
    const tab = await chrome.tabs.create({ url: editorUrl });

    console.log('Opened React video editor with session:', sessionId);

    // Optional: Download the video file automatically
    if (videoBlob) {
      const url = URL.createObjectURL(videoBlob);
      await chrome.downloads.download({
        url: url,
        filename: recordingData.videoFileName,
        saveAs: false
      });
    }

  } catch (error) {
    console.error('Error opening React video editor:', error);
  }
}

// Initialize extension
chrome.runtime.onInstalled.addListener(() => {
  console.log('Mouse Tracker & Screen Recorder installed');

  // Set initial storage values
  chrome.storage.sync.set({
    isRecording: false,
    reactAppUrl: 'http://localhost:3000' // Default React app URL
  });
});

// Handle external connections from React app
chrome.runtime.onConnectExternal.addListener((port) => {
  console.log('External connection from:', port.sender);

  port.onMessage.addListener(async (msg) => {
    if (msg.action === 'getRecordingData') {
      const sessionId = msg.sessionId;
      const result = await chrome.storage.local.get([sessionId]);

      if (result[sessionId]) {
        port.postMessage({
          success: true,
          data: result[sessionId]
        });

        // Clean up storage after successful retrieval
        await chrome.storage.local.remove([sessionId]);
      } else {
        port.postMessage({
          success: false,
          error: 'Recording data not found'
        });
      }
    }
  });
});
